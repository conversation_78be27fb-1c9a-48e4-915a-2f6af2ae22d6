using CertificateIssuer;
using System.Diagnostics;
using System.Diagnostics.Tracing;

const string EventSource = "CertificateIssuer";

if (!EventLog.SourceExists(EventSource))
{
    EventLog.CreateEventSource(EventSource, "Application");
}

EventLog.WriteEntry(EventSource, "Certificate Issuer started", EventLogEntryType.Information,10);

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

app.UseHttpsRedirection();

app.UseDefaultFiles();

app.UseStaticFiles();

app.MapGet("/Certificate", async (IConfiguration config, string apiKey, string domain, string subdomain, string? dnsArecordIP) =>
{
    if(apiKey != config["ApiKey"])
    {
        EventLog.WriteEntry(EventSource, $"Certificate Issuer called with wrong ApiKey: {apiKey}", EventLogEntryType.Information,20);

        return Results.Unauthorized();
    }

    string certpwd = config["CertPwd"];
    string workingfolder = config["WorkingFolder"];

    CertificateLib cl = new();

    EventLog.WriteEntry(EventSource, $"Certificate Issuer called with {workingfolder},{domain}, {subdomain}, {dnsArecordIP}, {certpwd}", EventLogEntryType.Information,30);

    CertResult cr = await cl.CreateOrUpdateCertificate(workingfolder, domain, subdomain, dnsArecordIP, certpwd);

    EventLog.WriteEntry(EventSource, $"Certificate Issuer completed", EventLogEntryType.Information, 40);

    return Results.Ok(cr);
})
.WithName("GetCertificate");

app.MapGet("/", () =>
{    
    return "Hello world!";
});

app.Run();



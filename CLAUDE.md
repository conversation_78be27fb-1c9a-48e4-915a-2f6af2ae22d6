# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

CertificateIssuer is a .NET 9.0 ASP.NET Core Web API application that automates SSL certificate generation using Let's Encrypt ACME protocol. The application integrates with Simply.com DNS API for DNS challenge validation and provides REST endpoints for certificate management.

## Architecture

### Core Components

- **Program.cs**: Main application entry point with Web API configuration and endpoint definitions
- **CertificateLib.cs**: Core certificate management logic using <PERSON><PERSON><PERSON> library for ACME protocol
- **CertResult.cs**: Data transfer object for certificate responses
- **appsettings.json**: Configuration including API keys, working folder paths, and certificate passwords

### Key Dependencies

- **Certes (3.0.4)**: ACME protocol client for Let's Encrypt integration
- **DnsClient (1.8.0)**: DNS query functionality for challenge validation
- **Microsoft.AspNetCore.OpenApi (9.0.3)**: OpenAPI/Swagger documentation

### API Endpoints

- `GET /Certificate`: Main certificate generation endpoint requiring apiKey, domain, subdomain, and optional dnsArecordIP parameters
- `GET /`: Health check endpoint returning "Hello world!"

## Development Commands

### Build and Run
```bash
# Build the solution
dotnet build CertificateIssuer.sln

# Run in development mode
dotnet run --project CertificateIssuer/CertificateIssuer.csproj

# Run with specific profile
dotnet run --project CertificateIssuer/CertificateIssuer.csproj --launch-profile https
```

### Project Structure
```
CertificateIssuer.sln              # Solution file
CertificateIssuer/
├── CertificateIssuer.csproj       # Project file targeting .NET 9.0
├── Program.cs                     # Main application and API endpoints
├── CertificateLib.cs              # ACME certificate logic
├── CertResult.cs                  # Response model
├── appsettings.json              # Configuration
└── Properties/launchSettings.json # Development profiles
```

### Configuration

The application requires configuration in appsettings.json:
- `ApiKey`: Authentication key for API access
- `CertPwd`: Password for generated certificate files
- `WorkingFolder`: Directory for storing ACME account keys and certificates

### Development Notes

- Application runs on https://localhost:7103 and http://localhost:5217 in development
- Uses Windows Event Log for logging (EventSource: "CertificateIssuer")
- Certificate generation involves DNS-01 challenge with Simply.com API
- ACME account keys are persisted in the working folder for reuse
- Supports both Debug and Release configurations for AnyCPU and x64 platforms
﻿using Certes;
using Certes.Acme;
using Certes.Acme.Resource;
using DnsClient;
using System.Net;
using System.Net.Http.Headers;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json.Nodes;

namespace CertificateIssuer
{
    public class CertificateLib
    {
        public async Task<CertResult> CreateOrUpdateCertificate(string workingfolder, string domain, string subdomain, string? dnsArecordIP, string certpwd)
        {
            AcmeContext acme;

            IAccountContext account;

            if (File.Exists($"{workingfolder}\\acme-account-key.pem"))
            {
                var keyPem = File.ReadAllText($"{workingfolder}\\acme-account-key.pem");
                var key = KeyFactory.FromPem(keyPem);

                acme = new AcmeContext(WellKnownServers.LetsEncryptV2, key);

                // You can fetch account info if needed:
                account = await acme.Account();

            }
            else
            {
                acme = new AcmeContext(WellKnownServers.LetsEncryptV2);
                account = await acme.NewAccount("<EMAIL>", true);

                var keyPem = acme.AccountKey.ToPem();

                File.WriteAllText($"{workingfolder}\\acme-account-key.pem", keyPem);
            }

            var order = await acme.NewOrder(new[] { $"{subdomain}.{domain}" });

            var authz = (await order.Authorizations()).First();
            var dnsChallenge = await authz.Dns();
            var dnsTxt = acme.AccountKey.DnsTxt(dnsChallenge.Token);
            var recordName = $"_acme-challenge.{subdomain}";


            HttpClient hc = new();

            hc.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes($"UE103752:8Jwv1VL7Z3")));
            hc.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

            var json = await hc.GetStringAsync($"https://api.simply.com/2/my/products/{domain}/dns/records");

            var array = JsonNode.Parse(json);

            var dnschallengerecord = array["records"].AsArray().Where(n => n["name"]?.GetValue<string>()?.StartsWith($"_acme-challenge.{subdomain}") ?? false).FirstOrDefault();
            
            if (dnschallengerecord != null)
            {
                var recordId = dnschallengerecord["record_id"].GetValue<int>();

                var delres = await hc.DeleteAsync($"https://api.simply.com/2/my/products/{domain}/dns/records/{recordId}");                
            }

            var dnsArecord = array["records"].AsArray().Where(n => n["name"]?.GetValue<string>()?.StartsWith($"{subdomain}") ?? false).FirstOrDefault();
            if (dnsArecord == null)
            {
                //var recordId = dnsArecord["record_id"].GetValue<int>();

                //var delres = await hc.DeleteAsync($"https://api.simply.com/2/my/products/{domain}/dns/records/{recordId}");

                if (!string.IsNullOrWhiteSpace(dnsArecordIP))
                {
                    var payloadArecord = $@"{{
  ""type"": ""A"",
  ""name"": ""{subdomain}.{domain}"",
  ""data"": ""{dnsArecordIP}"",
  ""priority"": 10,
  ""ttl"": 3600
}}";

                    var res = await hc.PostAsync($"https://api.simply.com/2/my/products/{domain}/dns/records", new StringContent(payloadArecord, Encoding.UTF8, "application/json"));
                }
            }

            var payloadTXT = $@"{{
  ""type"": ""TXT"",
  ""name"": ""_acme-challenge.{subdomain}"",
  ""data"": ""{dnsTxt}"",
  ""priority"": 10,
  ""ttl"": 300
}}";

            var res2 = await hc.PostAsync($"https://api.simply.com/2/my/products/{domain}/dns/records", new StringContent(payloadTXT, Encoding.UTF8, "application/json"));

            res2.EnsureSuccessStatusCode();

            var lookup = new LookupClient([IPAddress.Parse("*************")]);// ns1.simply.com
            IDnsQueryResponse result;
            do
            {
                await Task.Delay(5000);
                result = await lookup.QueryAsync($"_acme-challenge.{subdomain}.{domain}", QueryType.TXT); //Just check exists
            }
            while (!result.Answers.TxtRecords().Any(r => r.Text.Any(t => t.Contains(dnsTxt))));


            await dnsChallenge.Validate();

            while ((await order.Resource()).Status != OrderStatus.Ready)
            {
                await Task.Delay(3000);
            }

            var certKey = KeyFactory.NewKey(KeyAlgorithm.RS256);

            var cert = await order.Generate(new CsrInfo
            {
                CountryName = "DK",
                State = "-",
                Locality = "Copenhagen",
                Organization = "LHSoft",
                CommonName = $"{subdomain}.{domain}"
            }, certKey);

            var pfxBuilder = cert.ToPfx(certKey);

            var pfxBytes = pfxBuilder.Build($"{subdomain}.{domain}", certpwd);

            return new CertResult
            {
                Certificate = Convert.ToBase64String(pfxBytes)
            };

            //File.WriteAllBytes($"{workingfolder}{subdomain}cert.pfx", pfxBytes.Build($"{subdomain}.{domain}", certpwd));

            //var pemCert = cert.Certificate.ToPem();
            //var pemKey = certKey.ToPem();
            //File.WriteAllText($"{workingfolder}{subdomain}cert.pem", pemCert);
            //File.WriteAllText($"{workingfolder}{subdomain}key.pem", pemKey);


        }
    }
}
